# Optimized network operations for ultra-fast seat booking

import httpx
import requests
import json
import time
import random
import threading
import hashlib
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import urljoin

# ===== GLOBAL CONNECTION POOL =====

# Shared persistent clients for maximum connection reuse
HTTP_CLIENTS = {}
HTTP_CLIENTS_LOCK = threading.Lock()

# Per-thread session cache to eliminate thread contention
THREAD_LOCAL = threading.local()

# Pre-generated request templates
BROWSER_IDS = [''.join(f"{random.randint(0, 0xFFFF):04x}" for _ in range(4)) for _ in range(100)]
CACHED_UA = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'

# Base URLs - using IP for faster access
BASE_URL = 'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/'
HOLD_URL = urljoin(BASE_URL, 'events/groups/actions/hold-objects')
RELEASE_URL = urljoin(BASE_URL, 'events/groups/actions/release-held-objects')

# Thread pool for network operations
NETWORK_EXECUTOR = ThreadPoolExecutor(
    max_workers=40,  # Increased thread count for more parallelism
    thread_name_prefix="NetworkOps"
)

# ===== OPTIMIZED HTTP CLIENT FUNCTIONS =====

def get_http_client(proxy=None):
    """Get or create a thread-specific HTTP client for maximum performance"""
    # Use thread-local storage for per-thread clients
    if not hasattr(THREAD_LOCAL, 'clients'):
        THREAD_LOCAL.clients = {}
        
    proxy_key = str(proxy) if proxy else 'default'
    
    # Check thread-local cache first
    if proxy_key in THREAD_LOCAL.clients:
        return THREAD_LOCAL.clients[proxy_key]
    
    # Check global cache under lock
    with HTTP_CLIENTS_LOCK:
        if proxy_key in HTTP_CLIENTS:
            client = HTTP_CLIENTS[proxy_key]
            # Store in thread-local for future access
            THREAD_LOCAL.clients[proxy_key] = client
            return client
            
        # Create new client
        transport = httpx.HTTPTransport(
            http2=True,
            retries=1,
            limits=httpx.Limits(
                max_connections=100,
                max_keepalive_connections=50
            ),
            keepalive_expiry=60,  # Longer keepalive for connection reuse
            verify=False  # Disable SSL verification for seatsio IP connections
        )
        
        timeout = httpx.Timeout(
            connect=0.5,  # Fast connections
            read=0.75,    # Fast reads
            write=0.5,    # Fast writes
            pool=2.0      # Pool timeout
        )
        
        proxies = format_proxy_dict(proxy)
        
        client = httpx.Client(
            transport=transport,
            timeout=timeout,
            http2=True,
            proxies=proxies,
            verify=False  # Disable SSL verification for seatsio IP connections
        )
        
        # Store in both caches
        HTTP_CLIENTS[proxy_key] = client
        THREAD_LOCAL.clients[proxy_key] = client
        
        return client

def get_thread_session():
    """Get thread-local requests session for optimal connection reuse"""
    if not hasattr(THREAD_LOCAL, 'session'):
        session = requests.Session()
        
        # Configure for maximum performance
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=50,
            pool_maxsize=100,
            max_retries=1,
            pool_block=False
        )
        session.mount('http://', adapter)
        session.mount('https://', adapter)

        # Disable SSL verification for seatsio IP connections
        session.verify = False
        
        THREAD_LOCAL.session = session
        
    return THREAD_LOCAL.session

def format_proxy_dict(proxy_string):
    """Ultra-fast proxy formatting"""
    if not proxy_string:
        return None
        
    parts = proxy_string.split(":")
    
    if len(parts) == 4:
        return {
            "http://": f"http://{parts[2]}:{parts[3]}@{parts[0]}:{parts[1]}",
            "https://": f"http://{parts[2]}:{parts[3]}@{parts[0]}:{parts[1]}"
        }
    elif len(parts) == 2:
        return {
            "http://": f"http://{parts[0]}:{parts[1]}",
            "https://": f"http://{parts[0]}:{parts[1]}"
        }
    
    return None

# ===== OPTIMIZED REQUEST FUNCTIONS =====

@lru_cache(maxsize=2048)  # Massive cache for signatures
def generate_signature(body, token=None):
    """Ultra-optimized signature generation with caching"""
    if not token:
        # Request token only if needed and not provided
        from chart_token_manager import get_chart_token
        token = get_chart_token()
        
    # Handle case with no token
    if not token:
        return hashlib.sha256(body.encode("utf-8")).hexdigest()
    
    # One-shot signature generation
    return hashlib.sha256((token[::-1] + body).encode("utf-8")).hexdigest()

def prepare_hold_request(seat_id, token, event_key, channel_keys):
    """Pre-generate hold request for maximum performance"""
    # Fast channel keys optimization
    optimized_keys = _optimize_channel_keys(channel_keys)
    
    # Prepare template with minimum operations
    template = {
        'events': [event_key],
        'holdToken': token,
        'objects': [{'objectId': seat_id}],
        'channelKeys': optimized_keys,
        'validateEventsLinkedToSameChart': True
    }
    
    # Serialize to JSON
    body = json.dumps(template, separators=(',', ':'))
    
    # Generate signature
    signature = generate_signature(body)
    
    # Prepare headers
    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'content-type': 'application/json',
        'origin': 'https://cdn-eu.seatsio.net',
        'priority': 'u=1, i',
        'user-agent': CACHED_UA,
        'x-browser-id': random.choice(BROWSER_IDS),
        'x-client-tool': 'Renderer',
        'x-signature': signature
    }
    
    return body, headers

def prepare_release_request(seat_id, token, event_key):
    """Pre-generate release request for maximum performance"""
    # Prepare template with minimum operations
    template = {
        'events': [event_key],
        'holdToken': token,
        'objects': [{'objectId': seat_id}],
        'validateEventsLinkedToSameChart': True
    }
    
    # Serialize to JSON
    body = json.dumps(template, separators=(',', ':'))
    
    # Generate signature
    signature = generate_signature(body)
    
    # Prepare headers
    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'content-type': 'application/json',
        'origin': 'https://cdn-eu.seatsio.net',
        'priority': 'u=1, i',
        'user-agent': CACHED_UA,
        'x-browser-id': random.choice(BROWSER_IDS),
        'x-client-tool': 'Renderer',
        'x-signature': signature
    }
    
    return body, headers

def _optimize_channel_keys(channel_keys):
    """Optimize channel keys structure for maximum performance"""
    # Fast path for simple case
    if channel_keys == ['NO_CHANNEL'] or not channel_keys:
        return ['NO_CHANNEL']
        
    # Use cached optimization if possible
    cache_key = str(channel_keys)
    if hasattr(_optimize_channel_keys, '_cache') and cache_key in _optimize_channel_keys._cache:
        return _optimize_channel_keys._cache[cache_key]
        
    # Initialize cache if not exists
    if not hasattr(_optimize_channel_keys, '_cache'):
        _optimize_channel_keys._cache = {}
        
    # Process channel keys
    keys = ['NO_CHANNEL']  # Always include NO_CHANNEL for compatibility
    
    if isinstance(channel_keys, dict) and 'common' in channel_keys and channel_keys['common']:
        keys.append(channel_keys['common'][0])
    
    # Cache and return
    _optimize_channel_keys._cache[cache_key] = keys
    return keys

# ===== HIGH-PERFORMANCE API FUNCTIONS =====

def ultra_fast_hold(seat_id, token, event_key, channel_keys, proxy=None):
    """Ultra-optimized seat hold with minimal overhead"""
    start_time = time.time()
    
    try:
        # Prepare request with pre-generated components
        body, headers = prepare_hold_request(seat_id, token, event_key, channel_keys)
        
        # Get thread-local session
        session = get_thread_session()
        
        # Execute request with minimal overhead
        response = session.post(
            HOLD_URL,
            headers=headers,
            data=body,
            proxies=format_proxy_dict(proxy),
            timeout=1.0
        )
        
        # Process response without logging
        return response.status_code == 204
        
    except Exception:
        # Silent exception for maximum performance
        return False
        
    finally:
        # Record performance metrics if needed
        elapsed_ms = (time.time() - start_time) * 1000
        # Could emit metrics here if needed

def ultra_fast_release(seat_id, token, event_key, proxy=None):
    """Ultra-optimized seat release with minimal overhead"""
    try:
        # Prepare request
        body, headers = prepare_release_request(seat_id, token, event_key)
        
        # Get thread-local session
        session = get_thread_session()
        
        # Execute request with minimal overhead
        response = session.post(
            RELEASE_URL,
            headers=headers,
            data=body,
            proxies=format_proxy_dict(proxy),
            timeout=1.0
        )
        
        # Process response without logging
        return response.status_code == 204
        
    except Exception:
        # Silent exception for maximum performance
        return False

def ultra_fast_switch(seat_id, old_token, new_token, event_key, channel_keys, proxy=None):
    """Ultra-optimized seat switching with minimal gap between operations"""
    # Prepare both requests upfront to minimize gap
    release_body, release_headers = prepare_release_request(seat_id, old_token, event_key)
    hold_body, hold_headers = prepare_hold_request(seat_id, new_token, event_key, channel_keys)
    
    # Get thread-local session
    session = get_thread_session()
    proxies = format_proxy_dict(proxy)
    
    try:
        # Execute release and immediately prepare hold request
        release_response = session.post(
            RELEASE_URL,
            headers=release_headers,
            data=release_body,
            proxies=proxies,
            timeout=0.75
        )
        
        # Check if release succeeded
        if release_response.status_code != 204:
            return False
        
        # Immediately hold without any delay
        hold_response = session.post(
            HOLD_URL,
            headers=hold_headers,
            data=hold_body,
            proxies=proxies,
            timeout=0.75
        )
        
        # Check if hold succeeded
        return hold_response.status_code == 204
        
    except Exception:
        # Silent exception for maximum performance
        return False

# ===== CLEANUP FUNCTION =====

def cleanup_network_resources():
    """Clean up all network resources on shutdown"""
    # Close executor
    NETWORK_EXECUTOR.shutdown(wait=False)
    
    # Close all HTTP clients
    with HTTP_CLIENTS_LOCK:
        for client in HTTP_CLIENTS.values():
            try:
                client.close()
            except:
                pass
        HTTP_CLIENTS.clear()
    
    # Close thread-local sessions
    if hasattr(THREAD_LOCAL, 'session'):
        try:
            THREAD_LOCAL.session.close()
        except:
            pass