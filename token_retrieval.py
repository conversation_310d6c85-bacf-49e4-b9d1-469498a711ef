# token_retrieval.py - High-performance asynchronous hold token retrieval

import asyncio
import httpx
import logging
import threading
from typing import Optional, Dict, Any, List

# Import the new asynchronous account token manager functions
from account_token_manager import get_account_token, report_invalid_token, return_token

logger = logging.getLogger('webook_pro')

# --- Event ID Caching ---
# This remains thread-safe for simplicity, as it might be accessed from different contexts.
_event_id_cache: Dict[str, Any] = {}
_event_id_lock = threading.Lock()

def cache_event_id(event_data: Dict[str, Any]):
    """
    Extracts and caches the event ID from the event data response.
    """
    if not event_data or not isinstance(event_data, dict):
        return
    
    event_id = event_data.get("data", {}).get("_id")
    if event_id:
        with _event_id_lock:
            _event_id_cache["default"] = event_id
            logger.info(f"Cached new event ID: {event_id}")

def get_cached_event_id() -> Optional[str]:
    """Gets the most recently cached event ID."""
    with _event_id_lock:
        return _event_id_cache.get("default")

# --- Asynchronous Hold Token Retrieval ---

async def get_hold_token(
    event_id: str, 
    proxy: Optional[str] = None, 
    client: Optional[httpx.AsyncClient] = None
) -> Optional[str]:
    """
    Asynchronously gets a single hold token from the Webook API.

    Args:
        event_id: The event ID to get a hold token for.
        proxy: The proxy to use for the request.
        client: An optional existing httpx.AsyncClient to use.

    Returns:
        A hold token string, or None if it failed.
    """
    url = 'https://api.webook.com/api/v2/seats/hold-token'
    
    # 1. Get an account token from our high-speed manager
    account_token_info = await get_account_token()
    if not account_token_info:
        logger.error("Failed to get an account token from the manager.")
        return None
    
    access_token = account_token_info.get("access_token")

    headers = {
        'accept': 'application/json',
        'authorization': f'Bearer {access_token}',
        'content-type': 'application/json',
        'origin': 'https://webook.com',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/******** Firefox/121.0'
    }
    payload = {'event_id': event_id, 'lang': 'en'}

    try:
        # Use a provided client or create a new one for the request
        should_close_client = False
        if client is None:
            client = httpx.AsyncClient(proxies=proxy, timeout=5.0, verify=False)
            should_close_client = True

        response = await client.post(url, json=payload, headers=headers)

        if response.status_code in [401, 403]:
            logger.warning(f"Authentication failed for token {access_token[:8]}... Reporting as invalid.")
            await report_invalid_token(account_token_info)
            # Recursively try again with a new token.
            if should_close_client: await client.aclose()
            return await get_hold_token(event_id, proxy, client)

        if response.status_code != 200:
            logger.error(f"Error getting hold token: Status {response.status_code} - {response.text}")
            await return_token(account_token_info) # Return the token as it might be a temporary server issue
            return None

        data = response.json()
        hold_token = data.get('data', {}).get('token')

        if not hold_token:
            logger.error(f"No hold token in successful response: {data}")
            await return_token(account_token_info)
            return None

        logger.info(f"Successfully got hold token: {hold_token[:8]}... using account {account_token_info.get('email')}")
        await return_token(account_token_info) # Return the valid token for reuse
        return hold_token

    except httpx.RequestError as e:
        logger.error(f"HTTP request error getting hold token: {e}")
        await return_token(account_token_info) # Return token on network failure
        return None
    except Exception as e:
        logger.error(f"Unexpected error in get_hold_token: {e}")
        await return_token(account_token_info) # Return token on other errors
        return None
    finally:
        if should_close_client and client:
            await client.aclose()


async def create_hold_tokens_batch(
    count: int, 
    event_id: Optional[str] = None, 
    proxy: Optional[str] = None
) -> List[str]:
    """
    Creates a batch of hold tokens concurrently.

    Args:
        count: The number of hold tokens to create.
        event_id: The event ID to use. If None, uses the cached ID.
        proxy: The proxy to use for all requests.

    Returns:
        A list of successfully created hold token strings.
    """
    if not event_id:
        event_id = get_cached_event_id()
        if not event_id:
            logger.critical("Cannot create token batch: No Event ID is cached.")
            return []

    # Create a single client to be shared by all concurrent requests for this batch
    async with httpx.AsyncClient(proxies=proxy, timeout=10.0, verify=False) as client:
        # Create a list of tasks to run in parallel
        tasks = [get_hold_token(event_id, proxy=None, client=client) for _ in range(count)]
        
        # Run all tasks concurrently and wait for them to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)

    # Filter out any failures (which will be None or Exceptions)
    successful_tokens = [token for token in results if isinstance(token, str)]
    
    logger.info(f"Successfully created {len(successful_tokens)} of {count} requested hold tokens.")
    
    return successful_tokens
